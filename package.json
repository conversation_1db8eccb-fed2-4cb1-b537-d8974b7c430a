{"name": "sensefolks_survey-api", "version": "1.0.0", "description": "API for handling survey requests with a single endpoint for survey configuration", "main": "index.js", "scripts": {"dev": "nodemon", "build": "tsc"}, "repository": {"type": "git", "url": "**************-sensefolks:sensefolks/request-api__dev.git"}, "author": "<PERSON><PERSON> (Projckt)", "license": "ISC", "dependencies": {"@types/ioredis": "^4.28.10", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "geoip-lite": "^1.4.7", "ioredis": "^5.6.1", "joi": "^17.7.0", "pg": "^8.8.0", "pg-hstore": "^2.3.4", "sequelize": "^6.28.0", "ts-node": "^10.9.1", "typescript": "^4.9.4", "uuid": "^9.0.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.15", "@types/geoip-lite": "^1.4.1", "@types/node": "^18.11.18", "@types/uuid": "^9.0.0"}}