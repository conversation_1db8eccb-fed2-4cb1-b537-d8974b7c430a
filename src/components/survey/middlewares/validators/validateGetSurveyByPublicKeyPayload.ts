import Jo<PERSON> from 'joi';
import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';

/**
 * Schema for validating the public key parameter
 * Ensures the public key is a valid UUID
 */
const getSurveyByPublicKeyPayloadSchema = Joi.object({
  publicKey: Joi.string().uuid().required().messages({
    'string.empty': 'Public key cannot be empty',
    'string.uuid': 'Public key must be a valid ',
    'any.required': 'Public key is required',
  }),
});

/**
 * Middleware to validate the public key parameter
 * Returns a 400 error if the public key is invalid
 */
export const validateGetSurveyByPublicKeyPayload = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Validate the public key parameter
    const { error } = getSurveyByPublicKeyPayloadSchema.validate({
      publicKey: req.params.publicKey,
    });

    // Return a 400 error if validation fails
    if (error) {
      console.log(`${Var.app.emoji.failure} Invalid public key: ${req.params.publicKey}`);
      return res.status(400).json({
        success: false,
        message: 'Invalid public key',
        details: error.details[0].message,
      });
    }

    // Continue to the next middleware if validation passes
    console.log(`${Var.app.emoji.success} Valid public key: ${req.params.publicKey}`);
    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error in validateGetSurveyByPublicKeyPayload:`, error);
    return res.status(500).json({
      success: false,
      message: 'Error validating request',
    });
  }
};
